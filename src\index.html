<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8" />
  <title>財團法人原住民族語言研究發展基金會</title>
  <base href="/" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta property="og:image" content="assets/images/og.png" />
  <meta property="twitter:image" content="assets/images/og.png" />
  <!--GOOGLE 文字OCIN-->
  <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined" rel="stylesheet" />
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- Favicon -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;700;900&display=swap" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;700;900&display=swap" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@400;700;900&display=swap" rel="stylesheet" />
  <link
    href="https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
    rel="stylesheet" />
  <link rel="icon" type="image/svg+xml" href="assets/images/favicon.ico" />
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>

<body>

  <app-root></app-root>
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-D75P8X856J"></script>
  <script>
    // function googleTranslateElementInit() {
    //   new google.translate.TranslateElement(
    //     {
    //       pageLanguage: "zh-TW", // #TODO: 請輸入目前的網站語系 [h-02]
    //       gaTrack: true,
    //     },
    //     "google_translate_element"
    //   );
    //   document
    //     .querySelector("#google_translate_element select.goog-te-combo")
    //     .setAttribute("name", "select-google-translate");
    //   document
    //     .querySelector("#google_translate_element select.goog-te-combo")
    //     .setAttribute("id", "select-google-translate");
    //   document
    //     .querySelector("#google_translate_element_mb")
    //     .closest(".google-translate-btn")
    //     .remove();
    //   const genElList = document.querySelectorAll(
    //     "#goog-gt-tt, #goog-gt-tt *"
    //   );
    //   genElList.forEach((el) => {
    //     el.setAttribute("aria-hidden", true);
    //   });
    // }
    // function googleTranslateElementInitMb() {
    //   new google.translate.TranslateElement(
    //     {
    //       pageLanguage: "zh-TW", // #TODO: 請輸入目前的網站語系 [h-02]
    //       gaTrack: true,
    //     },
    //     "google_translate_element_mb"
    //   );
    //   document
    //     .querySelector("#google_translate_element_mb select.goog-te-combo")
    //     .setAttribute("name", "select-google-translate-mb");
    //   document
    //     .querySelector("#google_translate_element_mb select.goog-te-combo")
    //     .setAttribute("id", "select-google-translate-mb");
    //   document
    //     .querySelector("#google_translate_element")
    //     .closest(".google-translate-btn")
    //     .remove();
    //   const genElList = document.querySelectorAll(
    //     "#goog-gt-tt, #goog-gt-tt *"
    //   );
    //   genElList.forEach((el) => {
    //     el.setAttribute("aria-hidden", true);
    //   });
    // }
    // function googleTranslateInit() {
    //   var initFuncName =
    //     window.innerWidth < 992
    //       ? "googleTranslateElementInitMb"
    //       : "googleTranslateElementInit";
    //   var googleTranslateScriptTag = document.createElement("script");
    //   googleTranslateScriptTag.type = "text/javascript";
    //   googleTranslateScriptTag.id = "google-translate-script-tag";
    //   googleTranslateScriptTag.src =
    //     "//translate.google.com/translate_a/element.js?cb=" + initFuncName;
    //   document.head.appendChild(googleTranslateScriptTag);
    // }
    // googleTranslateInit();
  </script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const lang = sessionStorage.getItem('language');
      if (lang) {
        document.documentElement.setAttribute('lang', lang);
      }
    });
  </script>
</body>

</html>