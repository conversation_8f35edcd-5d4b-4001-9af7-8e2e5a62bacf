// Scss Document
html,
body {
	margin: 0 auto;
	padding: 0;
	height: 100%;
	box-sizing: border-box;
	background-color: #ebeadf;
	font-family: "Microsoft JhengHei UI", "Microsoft JhengHei UI Light", "微軟正黑體";
}
a {
	color: #177691;
}

.main-layout {
	margin: 133px 0 0;
	padding: 0;
	width: 100%;
	min-height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}

//內容區版型
.pages {
	&-layout {
		padding: 0 20px 40px;
		max-width: 1600px;
		width: 100%;
		box-sizing: border-box;
	}
	&-title {
		//padding-top: 40px;
		font-size: 3em;
		color: #214e57;
		text-align: center;
	}
	&-content {
		padding: 40px 40px 40px;
		border-radius: 22px;
		background-color: #fff;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		height: 100%;
		&-lsit {
			max-width: 1200px;
			width: 100%;
		}
	}
}

//頁籤
.tab {
	&-layout {
		margin: 0;
		padding: 20px 0;
		list-style: none;
	}
	&-content {
		margin: 0;
		padding: 0;
		list-style: none;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		gap: 1rem;
		&-item {
			&-link {
				padding: 12px 30px;
				display: block;
				overflow: hidden;
				text-decoration: none;
				background-color: #fff;
				border-radius: 40px;
				color: #202d3f;
				font-weight: bold;
				&:hover {
					background-color: #177691;
					color: #fff;
				}
			}
		}
		.active {
			.tab-content-item-link {
				background-color: #177691;
				color: #fff;
			}
		}
	}
}

//內容頁上稿
.edit-item {
	//段落標題
	&-paragraph-title {
		margin: 0;
		padding: 0 0 20px;
		font-size: 2em;
		color: #214e57;
		font-weight: bold;
		text-align: center;
	}
	&-subtitle {
		margin: 0;
		padding: 0 0 20px;
		font-size: 1.25em;
		font-weight: bold;
	}
	//文章
	&-text {
		padding: 0 0 20px;
		font-size: 1em;
		line-height: 1.6;
		letter-spacing: 0.125rem;
		font-weight: 400;
	}
	//圖片
	&-image {
		padding: 0 0 20px;
		img {
			border-radius: 22px;
			width: 100%;
			max-width: 100%;
			height: auto;
		}
	}
	//相簿
	&-album-wrapper {
		padding: 0 0 20px;
		position: relative;
		display: flex;
		align-items: flex-end;
		gap: 0.5rem;
		flex-direction: column;
	}
	&-album {
		position: relative;
		overflow: hidden;
		flex: 1;
		&:after {
			content: "";
			display: block;
			overflow: hidden;
			position: absolute;
			top: 0;
			right: 0;
			z-index: 100;
			background: linear-gradient(90deg, rgba(243, 243, 245, 0) 0%, #fff 58.85%);
			width: 40px;
			height: 100%;
		}
		&-track {
			display: flex;
			gap: 1rem;
			transition: transform 0.4s ease;
		}

		&-image {
			flex: 0 0 calc(100% / 4); // 一次 4 張
			border-radius: 22px;
			cursor: pointer;

			img {
				width: 100%;
				aspect-ratio: 16 / 9;
				object-fit: cover;
				border-radius: 22px;
			}
		}
	}
	//影片
	&-video {
		padding: 0 0 20px;
		&-frame {
			display: block;
			vertical-align: middle;
			border-radius: 22px;
			aspect-ratio: 16 / 9;
		}
		video {
			display: block;
			vertical-align: middle;
			border-radius: 22px;
			aspect-ratio: 16 / 9;
		}
	}
	//地圖
	&-map {
		padding: 0 0 20px;
		height: 320px;
		&-frame {
			width: 100%;
			height: 100%;
			-o-object-fit: cover;
			object-fit: cover;
			border-radius: 22px;
		}
	}
	//檔案
	&-files {
		padding: 0 0 20px;
		display: flex;
		width: 100%;
		&-list {
			display: flex;
			align-items: center;
			flex-direction: row;
			flex-wrap: wrap;
			justify-content: space-between;
			width: 100%;
			gap: 1rem;
			box-sizing: border-box;
			&-item {
				width: calc(50% - 1rem);
				&-link {
					padding: 20px;
					display: flex;
					align-items: center;
					flex-direction: row;
					justify-content: space-between;
					text-decoration: none;
					font-weight: bold;
					border-bottom: 1px solid #65676b;
					p {
						margin: 0;
						color: #202d3f;
						font-weight: bold;
					}
					span {
						color: #177691;
					}
					&:hover {
						border-radius: 20px;
						border-bottom: 1px solid transparent;
						background-color: #177691;
						p,
						span {
							margin: 0;
							color: #fff;
							font-weight: bold;
						}
					}
				}
			}
		}
	}
	//按鈕
	&-button {
		padding: 0 0 20px;
		text-align: center;
		&-link {
			padding: 16px 18px;
			display: inline-block;
			overflow: hidden;
			border-radius: 10px;
			background-color: #177691;
			color: #fff;
			font-weight: bold;
			text-decoration: none;
		}
	}
	//卡片
	&-card {
		margin: 0 0 20px;
		padding: 40px;
		display: flex;
		justify-content: space-between;
		align-items: flex-end;
		background: #fff url(../../assets/images/news/cards-bg.png) 0 0 repeat;
		border-radius: 22px;
		&-title {
			color: #fff;
			font-size: 2em;
			max-width: 674px;
			width: 100%;
			font-weight: bold;
		}
		&-cont {
			&-link {
				padding: 12px 30px;
				display: block;
				overflow: hidden;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #177691;
				background-color: #fff;
				border-radius: 40px;
				text-decoration: none;
				font-weight: bold;
				font-size: 1em;
			}
		}
	}
}

.album-nav {
	background: none;
	border: none;
	font-size: 2em;
	cursor: pointer;
	user-select: none;
}

//訂閱服務
.subscription-successful {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	&-title {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		span {
			font-size: 8em;
			color: #214e57;
		}
		p {
			margin: 0;
			padding: 10px;
			font-size: 1.5em;
			text-align: center;
			font-weight: bold;
		}
	}
	&-cont {
		padding: 20px 20px 40px;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		p {
			margin: 0;
			padding: 0;
			line-height: 1.6;
			font-size: 1.125em;
			text-align: left;
			width: 100%;
		}
	}
}

@media (max-width: 768px) {
	.main-layout {
		margin: 70px 0 0;
	}
}
