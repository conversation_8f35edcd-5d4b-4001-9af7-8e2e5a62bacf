import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LineService {

  constructor(private http: HttpClient) { }

  getLineList(): Observable<any> {
    return this.http.get<any>('api/New_News/GetNew_LineDataList');
  }

  getLine(id: string): Observable<any> {
    return this.http.get<any>(`api/Line/GetLineDetail/${id}`);
  }

}
