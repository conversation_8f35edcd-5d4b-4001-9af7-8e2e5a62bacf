import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ErrorPageComponent } from './shared/components/error-page/error-page.component';
import { HomeComponent } from './site/home/<USER>';
import { IndexComponent } from './site/index/index.component';
import { NewsListComponent } from './site/news/news-list/news-list.component';
import { NewsComponent } from './site/news/news/news.component';
import { ImageListComponent } from './site/image/image-list/image-list.component';
import { ImageComponent } from './site/image/image/image.component';
import { LineComponent } from './site/line/line/line.component';
import { LineListComponent } from './site/line/line-list/line-list.component';
import { FaqComponent } from './site/faq/faq.component';
import { HtmlComponent } from './site/html/html.component';
import { HtmlZipComponent } from './site/html-zip/html-zip.component';
import { ContentComponent } from './site/content/content.component';
import { TabComponent } from './site/tab/tab.component';
import { LinkComponent } from './site/link/link.component';
import { ShareResourceComponent } from './site/share-resource/share-resource.component';
import { VideoListComponent } from './site/video/video-list/video-list.component';
import { VideoComponent } from './site/video/video/video.component';
import { SurveyComponent } from './site/survey/survey.component';

const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  {
    path: '',
    component: IndexComponent,
    children: [
      {
        path: 'home',
        component: HomeComponent,
      },
      {
        //最新消息選單
        path: 'News',
        redirectTo: 'NewsList',
        pathMatch: 'full',
      },
      {
        path: 'NewsList',
        component: NewsListComponent,
      },
      {
        path: 'NewsDetail',
        component: NewsComponent,
      },
      {
        //圖片式選單
        path: 'Img',
        redirectTo: 'ImgList',
        pathMatch: 'full',
      },
      {
        path: 'ImgList',
        component: ImageListComponent,
      },
      {
        path: 'ImgDetail',
        component: ImageComponent,
      },
      {
        //條列式選單
        path: 'Line',
        redirectTo: 'LineList',
        pathMatch: 'full',
      },
      {
        path: 'LineList',
        component: LineListComponent,
      },
      {
        path: 'LineDetail',
        component: LineComponent,
      },
      {
        //Faq選單
        path: 'FAQ',
        component: FaqComponent,
      },
      {
        //html選單
        path: 'HTML',
        component: HtmlComponent,
      },
      {
        //HtmlZip選單
        path: 'HtmlZip',
        component: HtmlZipComponent,
      },
      {
        //Content選單
        path: 'Content',
        component: ContentComponent,
      },
      {
        //ShareResource
        path: 'ShareResource',
        component: ShareResourceComponent,
      },
      {
        //HyperLink
        path: 'HyperLink',
        component: LinkComponent,
      },
      {
        //Tab選單
        path: 'Tab',
        component: TabComponent,
      },
      {
        //Video選單
        path: 'Video',
        redirectTo: 'VideoList',
        pathMatch: 'full',
      },
      {
        path: 'VideoList',
        component: VideoListComponent,
      },
      {
        path: 'VideoDetail',
        component: VideoComponent,
      },
      {
        //問卷選單
        path: 'Survey',
        component: SurveyComponent,
      },
    ],
  },

  {
    path: 'error-page',
    component: ErrorPageComponent,
  },
  // {
  //   path: '**',
  //   redirectTo: '/error-page',
  // },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
