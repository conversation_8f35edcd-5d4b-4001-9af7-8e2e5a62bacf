import { Component, OnInit } from '@angular/core';
import { ContentService } from '../../core/services/content.service';
import { ShareService } from '../../core/services/share.service';
import { ActivatedRoute } from '@angular/router';
import { getContentDataResp } from '../../core/interface/content.interface';
import { dataItem } from '../../core/interface/type.interface';

@Component({
  selector: 'app-content',
  standalone: false,

  templateUrl: './content.component.html',
  styleUrl: './content.component.scss',
})
export class ContentComponent implements OnInit {
  menuItemId: string = '';
  title: string = '';
  contentData: dataItem[] = [];
  constructor(
    private activatedRoute: ActivatedRoute,
    private contentService: ContentService,
    private shareService: ShareService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.menuItemId = params.get('menuItemId')!;
    });
  }

  ngOnInit(): void {
    this.getContent();
  }

  getContent() {
    this.contentService.getContentData(this.menuItemId).subscribe({
      next: (resp: getContentDataResp) => {
        this.title = resp.data.titleName;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {},
    });
  }
}
