<!--內容放置區-->
<div class="pages-content">
    <div class="pages-content-lsit">
        @for(item of contentData; track item){
        @switch (item.tagName) {
        @case ('paragraphTitle') {
        <app-paragraph-title [paragraphTitleData]="item.tagData"></app-paragraph-title>
        }
        @case ('title') {
        <app-title [titleData]="item.tagData"></app-title>
        }
        @case ('content') {
        <app-content-template [contentData]="item.tagData"></app-content-template>
        }
        @case ('html') {
        <app-html-template [htmlData]="item.tagData"></app-html-template>
        }
        @case ('photo') {
        <app-photo [photoData]="item.tagData"></app-photo>
        }
        @case ('photoList') {
        <app-photo-list [photoListData]="item.tagData"></app-photo-list>
        }
        @case ('video') {
        <app-video-template [videoData]="item.tagData"></app-video-template>
        }
        @case ('address') {
        <app-address [addressData]="item.tagData"></app-address>
        }
        @case ('fileData') {
        <app-file-data [fileData]="item.tagData"></app-file-data>
        }
        @case ('button') {
        <app-button [buttonData]="item.tagData"></app-button>
        }
        @case ('card') {
        <app-card [cardData]="item.tagData"></app-card>
        }
        @case ('table') {
        <app-table></app-table>
        }
        }
        }
    </div>
</div>