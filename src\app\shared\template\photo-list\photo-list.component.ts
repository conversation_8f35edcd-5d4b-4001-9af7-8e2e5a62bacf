import {
  Component,
  ElementRef,
  HostListener,
  Input,
  ViewChild,
} from '@angular/core';
import { PhotoDatas } from '../../../core/interface/type.interface';

@Component({
  selector: 'app-photo-list',
  standalone: false,

  templateUrl: './photo-list.component.html',
  styleUrl: './photo-list.component.scss',
})
export class PhotoListComponent {
  @ViewChild('track') track!: ElementRef;
  @Input() photoListData: PhotoDatas = { photoDatas: [] };
  currentPage = 0;
  itemsPerPage = 4;
  imageCount = 0;
  totalPages = 0;

  ngAfterViewInit(): void {
    // 使用 ngAfterViewInit 確保 DOM 元素已經渲染
    this.imageCount = this.photoListData.photoDatas.length;
    this.totalPages = Math.ceil(this.imageCount / this.itemsPerPage);
    this.updateSlide();
  }

  // 使用 @HostListener 監聽視窗大小變化
  @HostListener('window:resize')
  onResize(): void {
    this.updateSlide();
  }

  // 更新投影片位置
  updateSlide(): void {
    if (this.track) {
      const slideWidth =
        this.track.nativeElement.offsetWidth / this.itemsPerPage;
      this.track.nativeElement.style.transform = `translateX(-${
        this.currentPage * slideWidth
      }px)`;
    }
  }

  // 導覽至下一頁
  nextPage(): void {
    if (this.currentPage < this.totalPages - 1) {
      this.currentPage++;
      this.updateSlide();
    }
  }

  // 導覽至上一頁
  prevPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
      this.updateSlide();
    }
  }
}
