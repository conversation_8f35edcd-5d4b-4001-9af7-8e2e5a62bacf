import { Component } from '@angular/core';
import { dataItem } from '../../../core/interface/type.interface';
import { NewsService } from '../../../core/services/news.service';
import { ActivatedRoute } from '@angular/router';
import { getNewsResp } from '../../../core/interface/news.interface';

@Component({
  selector: 'app-news',
  standalone: false,

  templateUrl: './news.component.html',
  styleUrl: './news.component.scss',
})
export class NewsComponent {
  contentData: dataItem[] = [];
  typeGroupId: string = '';
  title: string = '';
  constructor(
    private activatedRoute: ActivatedRoute,
    private newsService: NewsService
  ) {
    this.activatedRoute.queryParamMap.subscribe((params) => {
      this.getNews(params.get('typeGroupId')!);
    });
  }

  getNews(typeGroupId: string) {
    this.newsService.getNews(typeGroupId).subscribe({
      next: (resp: getNewsResp) => {
        this.title = resp.data.title;
        this.contentData = resp.data.new_NewsTagDatas;
      },
      error: () => {},
    });
  }
}
